{"name": "inventory-management-backend", "version": "1.0.0", "description": "Backend API for Inventory Management Dashboard", "main": "server.js", "scripts": {"start": "node server.js", "dev": "npx nodemon server.js", "test": "jest", "seed": "node scripts/seedData.js", "create-admin": "node scripts/createAdmin.js"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.0.0", "cors": "^2.8.5", "dotenv": "^16.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.0", "express-validator": "^6.14.3", "multer": "^1.4.5", "helmet": "^6.0.1", "express-rate-limit": "^6.7.0", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^2.0.20", "jest": "^29.4.0", "supertest": "^6.3.3"}, "keywords": ["inventory", "management", "dashboard", "mongodb", "express", "api"], "author": "Your Name", "license": "MIT"}