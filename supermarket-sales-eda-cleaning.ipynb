# Importing necessary libraries and suppressing warnings
import warnings
warnings.filterwarnings('ignore')

import numpy as np
import pandas as pd

import matplotlib
matplotlib.use('Agg')  # Use Agg backend to avoid GUI issues
import matplotlib.pyplot as plt
plt.switch_backend('Agg')  # In case only pyplot is used

import seaborn as sns

from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, confusion_matrix
from sklearn.preprocessing import LabelEncoder

%matplotlib inline

# Load the Supermarket Sales dataset
df = pd.read_csv('F:\SIC BigData\InventoryManagement\SuperMarket Analysis.csv', encoding='UTF-8-SIG', delimiter=',')

# A quick look at the first few rows of the dataset
df.head()

# Display basic information about the dataset
print('Shape of the dataframe:', df.shape)
print('\nData Types:')
print(df.dtypes)

# Overview of missing values if any
print('\nMissing values in each column:')
print(df.isnull().sum())

# Plot a count plot for the Payment column
plt.figure(figsize=(8, 4))
sns.countplot(data=df, x='Payment', palette='pastel')
plt.title('Distribution of Payment Methods')
plt.xlabel('Payment Method')
plt.ylabel('Count')
plt.tight_layout()
plt.show()

# Plotting histogram for the Rating column
plt.figure(figsize=(8, 4))
sns.histplot(df['Rating'], kde=True, color='teal')
plt.title('Distribution of Customer Ratings')
plt.xlabel('Rating')
plt.ylabel('Frequency')
plt.tight_layout()
plt.show()

# Preparing a correlation heatmap. We first reduce the dataframe to numeric columns.
numeric_df = df.select_dtypes(include=[np.number])

if numeric_df.shape[1] >= 4:
    plt.figure(figsize=(10, 8))
    corr = numeric_df.corr()
    sns.heatmap(corr, annot=True, cmap='coolwarm', fmt='.2f')
    plt.title('Correlation Heatmap for Numeric Features')
    plt.tight_layout()
    plt.show()
else:
    print('Not enough numeric columns for a correlation heatmap.')

# Creating a pair plot to visualize relationships between selected numeric features
selected_cols = ['Unit price', 'Quantity', 'Tax 5%', 'Sales', 'cogs', 'gross income', 'Rating']
sns.pairplot(df[selected_cols], kind='scatter', diag_kind='hist', plot_kws={'alpha':0.6})
plt.suptitle('Pair Plot of Selected Numeric Features', y=1.02)
plt.show()

# Convert 'Date' column to datetime type
df['Date'] = pd.to_datetime(df['Date'], format='%m/%d/%Y', errors='coerce')

# If there are errors converting dates, they will appear as NaT
if df['Date'].isnull().sum() > 0:
    print('Some dates could not be converted. Consider checking the format or handling NaT values.')

# Optionally, convert 'Time' column to datetime.time if needed
# Here we keep it as string or you may also use pd.to_datetime(df['Time'], format='%H:%M:%S').dt.time

# Check for duplicate rows
dupes = df.duplicated().sum()
print(f'Number of duplicate rows: {dupes}')

# Dropping duplicate rows if any
if dupes > 0:
    df.drop_duplicates(inplace=True)
    print('Duplicates have been dropped.')

# Final check for missing values
print('\nMissing values in each column after cleaning:')
print(df.isnull().sum())

# Building a predictor to classify the Payment method

from sklearn.pipeline import Pipeline
from sklearn.compose import ColumnTransformer
from sklearn.preprocessing import OneHotEncoder, StandardScaler

# Drop columns that are less useful for prediction
# We drop Invoice ID, Date, Time and Payment (the target) from features
features = df.drop(columns=['Invoice ID', 'Date', 'Time', 'Payment'])
target = df['Payment']

# Identify categorical and numerical columns
categorical_cols = features.select_dtypes(include=['object']).columns.tolist()
numerical_cols = features.select_dtypes(include=[np.number]).columns.tolist()

# Preprocessing for numerical data
numerical_transformer = StandardScaler()

# Preprocessing for categorical data
categorical_transformer = OneHotEncoder(drop='first', handle_unknown='ignore')

# Bundle preprocessing for both numeric and categorical data
preprocessor = ColumnTransformer(
    transformers=[
        ('num', numerical_transformer, numerical_cols),
        ('cat', categorical_transformer, categorical_cols)
    ])

# Define the model
model = RandomForestClassifier(random_state=42, n_estimators=100)

# Create and fit the pipeline
clf = Pipeline(steps=[('preprocessor', preprocessor), ('classifier', model)])

# Split the data into training and testing sets
X_train, X_test, y_train, y_test = train_test_split(features, target, test_size=0.2, random_state=42, stratify=target)

clf.fit(X_train, y_train)

# Make predictions and evaluate the model
y_pred = clf.predict(X_test)
accuracy = accuracy_score(y_test, y_pred)
print('Prediction Accuracy: {:.2f}%'.format(accuracy * 100))

# Plot the confusion matrix
cm = confusion_matrix(y_test, y_pred)
plt.figure(figsize=(6, 5))
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', cbar=False)
plt.title('Confusion Matrix for Payment Prediction')
plt.xlabel('Predicted')
plt.ylabel('Actual')
plt.tight_layout()
plt.show()

# Plot feature importances from the Random Forest
# Note: To extract feature names after transformation, we need to process the pipeline
preprocessed_features = preprocessor.fit_transform(X_train)

# Get numeric feature names
num_features = numerical_cols

# Get categorical feature names from OneHotEncoder
cat_features = list(preprocessor.named_transformers_['cat'].get_feature_names_out(categorical_cols))

all_features = num_features + cat_features

importances = model.feature_importances_
indices = np.argsort(importances)

plt.figure(figsize=(8, 10))
plt.barh(range(len(importances)), importances[indices], align='center', color='mediumseagreen')
plt.yticks(range(len(importances)), [all_features[i] for i in indices])
plt.xlabel('Feature Importance')
plt.title('Permutation Importance (Approximation) of Features')
plt.tight_layout()
plt.show()