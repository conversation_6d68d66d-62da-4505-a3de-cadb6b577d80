"use strict";

function _typeof(obj) { "@babel/helpers - typeof"; if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }; } return _typeof(obj); }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }

function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }

function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); if (superClass) _setPrototypeOf(subClass, superClass); }

function _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }

function _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }

function _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } return _assertThisInitialized(self); }

function _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return self; }

function _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }

function _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }

// Generated by CoffeeScript 2.5.1
var Padding, PaddingBottom, PaddingLeft, PaddingRight, PaddingTop, _Declaration;

_Declaration = require('./_Declaration');
PaddingTop = require('./PaddingTop');
PaddingLeft = require('./PaddingLeft');
PaddingRight = require('./PaddingRight');
PaddingBottom = require('./PaddingBottom');

module.exports = Padding = function () {
  var self;

  var Padding = /*#__PURE__*/function (_Declaration2) {
    _inherits(Padding, _Declaration2);

    var _super = _createSuper(Padding);

    function Padding() {
      _classCallCheck(this, Padding);

      return _super.apply(this, arguments);
    }

    _createClass(Padding, null, [{
      key: "setOnto",
      value: function setOnto(declarations, prop, originalValue) {
        var append, val, vals;
        append = '';
        val = _Declaration.sanitizeValue(originalValue);

        if (_Declaration.importantClauseRx.test(String(val))) {
          append = ' !important';
          val = val.replace(_Declaration.importantClauseRx, '');
        }

        val = val.trim();

        if (val.length === 0) {
          return self._setAllDirections(declarations, append, append, append, append);
        }

        vals = val.split(" ").map(function (val) {
          return val + append;
        });

        if (vals.length === 1) {
          return self._setAllDirections(declarations, vals[0], vals[0], vals[0], vals[0]);
        } else if (vals.length === 2) {
          return self._setAllDirections(declarations, vals[0], vals[1], vals[0], vals[1]);
        } else if (vals.length === 3) {
          return self._setAllDirections(declarations, vals[0], vals[1], vals[2], vals[1]);
        } else if (vals.length === 4) {
          return self._setAllDirections(declarations, vals[0], vals[1], vals[2], vals[3]);
        } else {
          throw Error("Can't understand value for padding: `".concat(originalValue, "`"));
        }
      }
    }, {
      key: "_setAllDirections",
      value: function _setAllDirections(declarations, top, right, bottom, left) {
        PaddingTop.setOnto(declarations, 'paddingTop', top);
        PaddingTop.setOnto(declarations, 'paddingRight', right);
        PaddingTop.setOnto(declarations, 'paddingBottom', bottom);
        PaddingTop.setOnto(declarations, 'paddingLeft', left);
      }
    }]);

    return Padding;
  }(_Declaration);

  ;
  self = Padding;
  return Padding;
}.call(void 0);