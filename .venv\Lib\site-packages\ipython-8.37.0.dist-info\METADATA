Metadata-Version: 2.4
Name: ipython
Version: 8.37.0
Summary: IPython: Productive Interactive Computing
Author: The IPython Development Team
Author-email: <EMAIL>
License: BSD-3-Clause
Project-URL: Homepage, https://ipython.org
Project-URL: Documentation, https://ipython.readthedocs.io/
Project-URL: Funding, https://numfocus.org/
Project-URL: Source, https://github.com/ipython/ipython
Project-URL: Tracker, https://github.com/ipython/ipython/issues
Keywords: Interactive,Interpreter,Shell,Embedding
Platform: Linux
Platform: Mac OSX
Platform: Windows
Classifier: Framework :: IPython
Classifier: Framework :: Jupyter
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Topic :: System :: Shells
Requires-Python: >=3.10
Description-Content-Type: text/x-rst
License-File: LICENSE
Requires-Dist: colorama; sys_platform == "win32"
Requires-Dist: decorator
Requires-Dist: exceptiongroup; python_version < "3.11"
Requires-Dist: jedi>=0.16
Requires-Dist: matplotlib-inline
Requires-Dist: pexpect>4.3; sys_platform != "win32" and sys_platform != "emscripten"
Requires-Dist: prompt_toolkit<3.1.0,>=3.0.41
Requires-Dist: pygments>=2.4.0
Requires-Dist: stack_data
Requires-Dist: traitlets>=5.13.0
Requires-Dist: typing_extensions>=4.6; python_version < "3.12"
Provides-Extra: black
Requires-Dist: black; extra == "black"
Provides-Extra: doc
Requires-Dist: docrepr; extra == "doc"
Requires-Dist: exceptiongroup; extra == "doc"
Requires-Dist: intersphinx_registry; extra == "doc"
Requires-Dist: ipykernel; extra == "doc"
Requires-Dist: ipython[test]; extra == "doc"
Requires-Dist: matplotlib; extra == "doc"
Requires-Dist: setuptools>=18.5; extra == "doc"
Requires-Dist: sphinx-rtd-theme; extra == "doc"
Requires-Dist: sphinx>=1.3; extra == "doc"
Requires-Dist: sphinxcontrib-jquery; extra == "doc"
Requires-Dist: tomli; python_version < "3.11" and extra == "doc"
Requires-Dist: typing_extensions; extra == "doc"
Provides-Extra: kernel
Requires-Dist: ipykernel; extra == "kernel"
Provides-Extra: nbconvert
Requires-Dist: nbconvert; extra == "nbconvert"
Provides-Extra: nbformat
Requires-Dist: nbformat; extra == "nbformat"
Provides-Extra: notebook
Requires-Dist: ipywidgets; extra == "notebook"
Requires-Dist: notebook; extra == "notebook"
Provides-Extra: parallel
Requires-Dist: ipyparallel; extra == "parallel"
Provides-Extra: qtconsole
Requires-Dist: qtconsole; extra == "qtconsole"
Provides-Extra: terminal
Provides-Extra: test
Requires-Dist: pytest; extra == "test"
Requires-Dist: pytest-asyncio<0.22; extra == "test"
Requires-Dist: testpath; extra == "test"
Requires-Dist: pickleshare; extra == "test"
Requires-Dist: packaging; extra == "test"
Provides-Extra: test-extra
Requires-Dist: ipython[test]; extra == "test-extra"
Requires-Dist: curio; extra == "test-extra"
Requires-Dist: jupyter_ai; extra == "test-extra"
Requires-Dist: matplotlib!=3.2.0; extra == "test-extra"
Requires-Dist: nbformat; extra == "test-extra"
Requires-Dist: numpy>=1.23; extra == "test-extra"
Requires-Dist: pandas; extra == "test-extra"
Requires-Dist: trio; extra == "test-extra"
Provides-Extra: matplotlib
Requires-Dist: matplotlib; extra == "matplotlib"
Provides-Extra: all
Requires-Dist: ipython[black,doc,kernel,matplotlib,nbconvert,nbformat,notebook,parallel,qtconsole]; extra == "all"
Requires-Dist: ipython[test,test_extra]; extra == "all"
Dynamic: author
Dynamic: author-email
Dynamic: license
Dynamic: license-file

IPython provides a rich toolkit to help you make the most out of using Python
interactively.  Its main components are:

 * A powerful interactive Python shell
 * A `Jupyter <https://jupyter.org/>`_ kernel to work with Python code in Jupyter
   notebooks and other interactive frontends.

The enhanced interactive Python shells have the following main features:

 * Comprehensive object introspection.

 * Input history, persistent across sessions.

 * Caching of output results during a session with automatically generated
   references.

 * Extensible tab completion, with support by default for completion of python
   variables and keywords, filenames and function keywords.

 * Extensible system of 'magic' commands for controlling the environment and
   performing many tasks related either to IPython or the operating system.

 * A rich configuration system with easy switching between different setups
   (simpler than changing $PYTHONSTARTUP environment variables every time).

 * Session logging and reloading.

 * Extensible syntax processing for special purpose situations.

 * Access to the system shell with user-extensible alias system.

 * Easily embeddable in other Python programs and GUIs.

 * Integrated access to the pdb debugger and the Python profiler.

The latest development version is always available from IPython's `GitHub
site <http://github.com/ipython>`_.
