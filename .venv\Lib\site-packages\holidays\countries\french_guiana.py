#  holidays
#  --------
#  A fast, efficient Python library for generating country, province and state
#  specific sets of holidays on the fly. It aims to make determining whether a
#  specific date is a holiday as fast and flexible as possible.
#
#  Authors: <AUTHORS>
# <AUTHOR> <EMAIL> (c) 2017-2023
# <AUTHOR> <EMAIL> (c) 2014-2017
#  Website: https://github.com/vacanza/holidays
#  License: MIT (see LICENSE file)

from holidays.countries.france import France
from holidays.mixins.child_entity import ChildEntity


class HolidaysGF(ChildEntity, France):
    """French Guiana holidays.

    Alias of a French subdivision that is also officially assigned
    its own country code in ISO 3166-1.

    References:
        * <https://en.wikipedia.org/wiki/French_Guiana>
        * <https://en.wikipedia.org/wiki/Public_holidays_in_France>
    """

    country = "GF"
    parent_entity = France
    parent_entity_subdivision_code = "973"
    # Cession from Portugal on May 30th, 1814.
    start_year = 1815


class FrenchGuiana(HolidaysGF):
    pass


class GF(HolidaysGF):
    pass


class GUF(HolidaysGF):
    pass
