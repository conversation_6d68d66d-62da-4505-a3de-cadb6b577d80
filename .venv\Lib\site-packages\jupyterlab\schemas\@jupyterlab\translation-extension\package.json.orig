{"name": "@jupyterlab/translation-extension", "version": "4.4.5", "description": "JupyterLab - Translation services", "keywords": ["jup<PERSON><PERSON>", "jupyterlab", "jupyterlab-extension"], "homepage": "https://github.com/jupyterlab/jupyterlab", "bugs": {"url": "https://github.com/jupyterlab/jupyterlab/issues"}, "repository": {"type": "git", "url": "https://github.com/jupyterlab/jupyterlab.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": "Project Jupyter", "sideEffects": ["style/*.css", "style/index.js"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "files": ["lib/**/*.{d.ts,eot,gif,html,jpg,js,js.map,json,png,svg,woff2,ttf}", "schema/**/*.{json,}", "style/**/*.{css,eot,gif,html,jpg,json,png,svg,woff2,ttf}", "style/index.js", "src/**/*.{ts,tsx}"], "scripts": {"build": "tsc", "clean": "rimraf lib tsconfig.tsbuildinfo", "watch": "tsc -w"}, "dependencies": {"@jupyterlab/application": "^4.4.5", "@jupyterlab/apputils": "^4.5.5", "@jupyterlab/mainmenu": "^4.4.5", "@jupyterlab/settingregistry": "^4.4.5", "@jupyterlab/translation": "^4.4.5"}, "devDependencies": {"rimraf": "~5.0.5", "typescript": "~5.5.4"}, "publishConfig": {"access": "public"}, "jupyterlab": {"extension": true, "schemaDir": "schema"}, "styleModule": "style/index.js"}