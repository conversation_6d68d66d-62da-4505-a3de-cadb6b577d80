#!/bin/sh
export TBBROOT="C:/Users/<USER>/AppData/Local/Temp/tmptu5gwzgz/cmdstan-2.33.1/stan/lib/stan_math/lib/tbb_2020.3"
export TBB_ARCH_PLATFORM="intel64\mingw8.3.0"
export TBB_TARGET_ARCH="intel64"
export CPATH="${TBBROOT}/include;$CPATH"
export LIBRARY_PATH="C:/Users/<USER>/AppData/Local/Temp/tmptu5gwzgz/cmdstan-2.33.1/stan/lib/stan_math/lib/tbb;$LIBRARY_PATH"
export PATH="C:/Users/<USER>/AppData/Local/Temp/tmptu5gwzgz/cmdstan-2.33.1/stan/lib/stan_math/lib/tbb;$PATH"
export LD_LIBRARY_PATH="C:/Users/<USER>/AppData/Local/Temp/tmptu5gwzgz/cmdstan-2.33.1/stan/lib/stan_math/lib/tbb;$LD_LIBRARY_PATH"
