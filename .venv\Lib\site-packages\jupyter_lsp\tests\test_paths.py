import pathlib
import platform
import sys

import pytest

from ..paths import file_uri_to_path, is_relative, normalized_uri

WIN = platform.system() == "Windows"
HOME = pathlib.Path("~").expanduser()
PY35 = sys.version_info[:2] == (3, 5)


@pytest.mark.skipif(WIN, reason="can't test POSIX paths on Windows")
@pytest.mark.parametrize("root_dir, expected_root_uri", [["~", HOME.as_uri()]])
def test_normalize_posix_path_home(root_dir, expected_root_uri):  # pragma: no cover
    assert normalized_uri(root_dir) == expected_root_uri


@pytest.mark.skipif(WIN, reason="can't test POSIX paths on Windows")
@pytest.mark.parametrize(
    "root, path",
    [["~", "~/a"], ["~", "~/a/../b/"], ["/", "/"], ["/a", "/a/b"], ["/a", "/a/b/../c"]],
)
def test_is_relative_ok(root, path):
    assert is_relative(root, path)


@pytest.mark.skipif(WIN, reason="can't test POSIX paths on Windows")
@pytest.mark.parametrize(
    "root, path",
    [
        ["~", "~/.."],
        ["~", "/"],
        ["/a", "/"],
        ["/a/b", "/a"],
        ["/a/b", "/a/b/.."],
        ["/a", "/a/../b"],
        ["/a", "a//"],
    ],
)
def test_is_relative_not_ok(root, path):
    assert not is_relative(root, path)


@pytest.mark.skipif(not WIN, reason="can't test Windows paths on POSIX")
@pytest.mark.parametrize(
    "root, path",
    [
        ["c:\\Users\\<USER>\\Users\\"],
        ["c:\\Users\\<USER>\\"],
        ["c:\\Users", "c:\\Users\\<USER>\\Users\\user1", "file:///c:/Users/<USER>"],
        ["C:\\Users\\<USER>