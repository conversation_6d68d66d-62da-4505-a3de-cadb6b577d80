#  holidays
#  --------
#  A fast, efficient Python library for generating country, province and state
#  specific sets of holidays on the fly. It aims to make determining whether a
#  specific date is a holiday as fast and flexible as possible.
#
#  Authors: <AUTHORS>
# <AUTHOR> <EMAIL> (c) 2017-2023
# <AUTHOR> <EMAIL> (c) 2014-2017
#  Website: https://github.com/vacanza/holidays
#  License: MIT (see LICENSE file)

from datetime import date
from typing import Optional

from holidays.calendars.custom import _CustomCalendar
from holidays.calendars.gregorian import JAN, FEB, MAR, MAY, JUN, OCT, NOV, DEC

BUDDHA_DAY = "BUDDHA_DAY"
GENGHIS_KHAN_DAY = "GENGHIS_KHAN_DAY"
TSAGAAN_SAR = "TSAGAAN_SAR"


class _MongolianLunisolar:
    BUDDHA_DAY_DATES = {
        2004: (JUN, 3),
        2005: (MAY, 23),
        2006: (JUN, 11),
        2007: (MAY, 31),
        2008: (MAY, 20),
        2009: (JUN, 7),
        2010: (MAY, 27),
        2011: (MAY, 17),
        2012: (JUN, 4),
        2013: (MAY, 25),
        2014: (JUN, 13),
        2015: (JUN, 2),
        2016: (MAY, 21),
        2017: (JUN, 9),
        2018: (MAY, 29),
        2019: (MAY, 18),
        2020: (JUN, 5),
        2021: (MAY, 26),
        2022: (JUN, 14),
        2023: (JUN, 4),
        2024: (MAY, 23),
        2025: (JUN, 11),
        2026: (MAY, 31),
        2027: (MAY, 20),
        2028: (JUN, 7),
        2029: (MAY, 27),
        2030: (MAY, 17),
        2031: (JUN, 5),
        2032: (MAY, 25),
        2033: (JUN, 12),
        2034: (JUN, 2),
        2035: (MAY, 22),
        2036: (JUN, 8),
        2037: (MAY, 29),
        2038: (MAY, 18),
        2039: (JUN, 6),
        2040: (MAY, 26),
        2041: (JUN, 14),
        2042: (JUN, 3),
        2043: (MAY, 23),
        2044: (JUN, 10),
        2045: (MAY, 30),
        2046: (MAY, 20),
        2047: (JUN, 8),
        2048: (MAY, 27),
        2049: (MAY, 17),
        2050: (JUN, 5),
        2051: (MAY, 25),
        2052: (JUN, 12),
        2053: (JUN, 1),
        2054: (MAY, 21),
        2055: (JUN, 9),
        2056: (MAY, 29),
        2057: (MAY, 18),
        2058: (JUN, 6),
        2059: (MAY, 27),
        2060: (JUN, 14),
        2061: (JUN, 3),
        2062: (MAY, 23),
        2063: (JUN, 11),
        2064: (MAY, 30),
        2065: (MAY, 20),
        2066: (JUN, 8),
        2067: (MAY, 28),
        2068: (JUN, 15),
        2069: (JUN, 4),
        2070: (MAY, 24),
        2071: (JUN, 12),
        2072: (MAY, 31),
        2073: (MAY, 21),
        2074: (JUN, 9),
        2075: (MAY, 30),
        2076: (MAY, 18),
        2077: (JUN, 6),
        2078: (MAY, 26),
        2079: (JUN, 14),
        2080: (JUN, 2),
        2081: (MAY, 22),
        2082: (JUN, 10),
        2083: (MAY, 31),
        2084: (MAY, 20),
        2085: (JUN, 8),
        2086: (MAY, 28),
        2087: (JUN, 16),
        2088: (JUN, 4),
        2089: (MAY, 24),
        2090: (JUN, 12),
        2091: (JUN, 1),
        2092: (MAY, 21),
        2093: (JUN, 9),
        2094: (MAY, 30),
        2095: (MAY, 19),
        2096: (JUN, 6),
        2097: (MAY, 26),
        2098: (JUN, 13),
        2099: (JUN, 3),
        2100: (MAY, 23),
    }

    GENGHIS_KHAN_DAY_DATES = {
        2004: (NOV, 13),
        2005: (NOV, 2),
        2006: (NOV, 21),
        2007: (NOV, 10),
        2008: (NOV, 28),
        2009: (NOV, 17),
        2010: (NOV, 7),
        2011: (NOV, 26),
        2012: (NOV, 14),
        2013: (NOV, 4),
        2014: (NOV, 23),
        2015: (NOV, 12),
        2016: (OCT, 31),
        2017: (NOV, 19),
        2018: (NOV, 8),
        2019: (NOV, 27),
        2020: (NOV, 16),
        2021: (NOV, 5),
        2022: (NOV, 24),
        2023: (NOV, 14),
        2024: (NOV, 2),
        2025: (NOV, 21),
        2026: (NOV, 10),
        2027: (NOV, 29),
        2028: (NOV, 17),
        2029: (NOV, 7),
        2030: (NOV, 26),
        2031: (NOV, 15),
        2032: (NOV, 4),
        2033: (NOV, 22),
        2034: (NOV, 11),
        2035: (NOV, 30),
        2036: (NOV, 18),
        2037: (NOV, 8),
        2038: (NOV, 27),
        2039: (NOV, 17),
        2040: (NOV, 5),
        2041: (NOV, 24),
        2042: (NOV, 13),
        2043: (NOV, 2),
        2044: (NOV, 20),
        2045: (NOV, 9),
        2046: (NOV, 28),
        2047: (NOV, 18),
        2048: (NOV, 7),
        2049: (NOV, 26),
        2050: (NOV, 15),
        2051: (NOV, 4),
        2052: (NOV, 22),
        2053: (NOV, 11),
        2054: (NOV, 30),
        2055: (NOV, 19),
        2056: (NOV, 8),
        2057: (NOV, 27),
        2058: (NOV, 17),
        2059: (NOV, 6),
        2060: (NOV, 24),
        2061: (NOV, 13),
        2062: (NOV, 2),
        2063: (NOV, 21),
        2064: (NOV, 9),
        2065: (NOV, 28),
        2066: (NOV, 18),
        2067: (NOV, 7),
        2068: (NOV, 25),
        2069: (NOV, 14),
        2070: (NOV, 3),
        2071: (NOV, 22),
        2072: (NOV, 11),
        2073: (NOV, 30),
        2074: (NOV, 19),
        2075: (NOV, 9),
        2076: (NOV, 27),
        2077: (NOV, 16),
        2078: (NOV, 5),
        2079: (NOV, 24),
        2080: (NOV, 12),
        2081: (NOV, 1),
        2082: (NOV, 21),
        2083: (NOV, 10),
        2084: (NOV, 28),
        2085: (NOV, 18),
        2086: (NOV, 7),
        2087: (NOV, 26),
        2088: (NOV, 14),
        2089: (NOV, 3),
        2090: (NOV, 22),
        2091: (NOV, 12),
        2092: (NOV, 30),
        2093: (NOV, 19),
        2094: (NOV, 9),
        2095: (NOV, 27),
        2096: (NOV, 15),
        2097: (NOV, 5),
        2098: (NOV, 23),
        2099: (NOV, 13),
        2100: (DEC, 2),
    }

    TSAGAAN_SAR_DATES = {
        2004: (FEB, 21),
        2005: (FEB, 9),
        2006: (JAN, 30),
        2007: (FEB, 18),
        2008: (FEB, 8),
        2009: (FEB, 25),
        2010: (FEB, 14),
        2011: (FEB, 3),
        2012: (FEB, 22),
        2013: (FEB, 11),
        2014: (JAN, 31),
        2015: (FEB, 19),
        2016: (FEB, 9),
        2017: (FEB, 27),
        2018: (FEB, 16),
        2019: (FEB, 5),
        2020: (FEB, 24),
        2021: (FEB, 12),
        2022: (FEB, 2),
        2023: (FEB, 21),
        2024: (FEB, 10),
        2025: (MAR, 1),
        2026: (FEB, 18),
        2027: (FEB, 7),
        2028: (FEB, 26),
        2029: (FEB, 14),
        2030: (FEB, 3),
        2031: (FEB, 22),
        2032: (FEB, 12),
        2033: (JAN, 31),
        2034: (FEB, 19),
        2035: (FEB, 9),
        2036: (FEB, 28),
        2037: (FEB, 16),
        2038: (FEB, 5),
        2039: (FEB, 24),
        2040: (FEB, 13),
        2041: (FEB, 2),
        2042: (FEB, 21),
        2043: (FEB, 10),
        2044: (FEB, 29),
        2045: (FEB, 17),
        2046: (FEB, 6),
        2047: (FEB, 25),
        2048: (FEB, 14),
        2049: (FEB, 3),
        2050: (FEB, 22),
        2051: (FEB, 12),
        2052: (FEB, 1),
        2053: (FEB, 19),
        2054: (FEB, 8),
        2055: (FEB, 27),
        2056: (FEB, 16),
        2057: (FEB, 4),
        2058: (FEB, 23),
        2059: (FEB, 13),
        2060: (FEB, 3),
        2061: (FEB, 21),
        2062: (FEB, 10),
        2063: (MAR, 1),
        2064: (FEB, 18),
        2065: (FEB, 6),
        2066: (FEB, 25),
        2067: (FEB, 14),
        2068: (FEB, 4),
        2069: (FEB, 22),
        2070: (FEB, 12),
        2071: (FEB, 1),
        2072: (FEB, 19),
        2073: (FEB, 7),
        2074: (FEB, 26),
        2075: (FEB, 16),
        2076: (FEB, 5),
        2077: (FEB, 23),
        2078: (FEB, 13),
        2079: (FEB, 2),
        2080: (FEB, 21),
        2081: (FEB, 9),
        2082: (FEB, 28),
        2083: (FEB, 17),
        2084: (FEB, 7),
        2085: (FEB, 25),
        2086: (FEB, 14),
        2087: (FEB, 4),
        2088: (FEB, 23),
        2089: (FEB, 11),
        2090: (MAR, 2),
        2091: (FEB, 19),
        2092: (FEB, 8),
        2093: (FEB, 26),
        2094: (FEB, 16),
        2095: (FEB, 5),
        2096: (FEB, 24),
        2097: (FEB, 13),
        2098: (FEB, 2),
        2099: (FEB, 21),
        2100: (FEB, 10),
    }

    def _get_holiday(self, holiday: str, year: int) -> tuple[Optional[date], bool]:
        estimated_dates = getattr(self, f"{holiday}_DATES", {})
        exact_dates = getattr(self, f"{holiday}_DATES_{_CustomCalendar.CUSTOM_ATTR_POSTFIX}", {})
        dt = exact_dates.get(year, estimated_dates.get(year, ()))
        return date(year, *dt) if dt else None, year not in exact_dates

    def buddha_day_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(BUDDHA_DAY, year)

    def genghis_khan_day_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(GENGHIS_KHAN_DAY, year)

    def tsagaan_sar_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(TSAGAAN_SAR, year)


class _CustomMongolianHolidays(_CustomCalendar, _MongolianLunisolar):
    pass
